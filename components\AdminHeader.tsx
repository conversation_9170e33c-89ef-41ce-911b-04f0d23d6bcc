'use client'

import React from 'react'
import { Search, Bell, MessageSquare, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  SidebarTrigger,
} from '@/components/ui/sidebar'

interface AdminHeaderProps {
  title?: string
}

export function AdminHeader({ title = "Admin Dashboard" }: AdminHeaderProps) {
  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
      <SidebarTrigger className="-ml-1" />
      
      <div className="flex flex-1 items-center gap-4">
        <h1 className="text-lg font-semibold">{title}</h1>
        
        <div className="ml-auto flex items-center gap-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="What do you want to find?"
              className="w-[300px] pl-8"
            />
          </div>
          
          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
              3
            </span>
          </Button>
          
          {/* Messages */}
          <Button variant="ghost" size="icon" className="relative">
            <MessageSquare className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-blue-500 text-[10px] font-medium text-white flex items-center justify-center">
              2
            </span>
          </Button>
          
          {/* User Profile */}
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center">
              <img
                src="/api/placeholder/32/32"
                alt="Priscilla Lily"
                className="h-8 w-8 rounded-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling!.textContent = 'PL';
                }}
              />
              <span className="text-white text-sm font-medium hidden">PL</span>
            </div>
            <div className="hidden md:block">
              <div className="text-sm font-medium">Priscilla Lily</div>
              <div className="text-xs text-muted-foreground">Admin</div>
            </div>
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
      </div>
    </header>
  )
}
