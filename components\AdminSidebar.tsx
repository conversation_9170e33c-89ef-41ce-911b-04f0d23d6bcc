'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar'
import {
  Home,
  Users,
  GraduationCap,
  BookOpen,
  Calendar,
  ClipboardList,
  UserCheck,
  Bell,
  Bus,
  Building,
  Settings,
  LogOut,
} from 'lucide-react'

// Navigation items for the admin panel
const navigationItems = [
  {
    title: 'Overview',
    items: [
      {
        title: 'Dashboard',
        url: '/',
        icon: Home,
      },
    ],
  },
  {
    title: 'User Management',
    items: [
      {
        title: 'Students',
        url: '/students',
        icon: Users,
      },
      {
        title: 'Teachers',
        url: '/teachers',
        icon: GraduationCap,
      },
      {
        title: 'Parents',
        url: '/parents',
        icon: Users,
      },
    ],
  },
  {
    title: 'Academic',
    items: [
      {
        title: 'Classes',
        url: '/classes',
        icon: BookOpen,
      },
      {
        title: 'Subjects',
        url: '/subjects',
        icon: BookOpen,
      },
      {
        title: 'Routine',
        url: '/routine',
        icon: Calendar,
      },
      {
        title: 'Exams',
        url: '/exams',
        icon: ClipboardList,
      },
      {
        title: 'Attendance',
        url: '/attendance',
        icon: UserCheck,
      },
    ],
  },
  {
    title: 'Communication',
    items: [
      {
        title: 'Notices',
        url: '/notices',
        icon: Bell,
      },
    ],
  },
  {
    title: 'Operations',
    items: [
      {
        title: 'Transport',
        url: '/transport',
        icon: Bus,
      },
      {
        title: 'Hostel',
        url: '/hostel',
        icon: Building,
      },
    ],
  },
]

export function AdminSidebar() {
  const pathname = usePathname()

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <GraduationCap className="h-4 w-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">ia Academy</span>
            <span className="truncate text-xs text-muted-foreground">Admin Panel</span>
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        {navigationItems.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <item.icon className="h-4 w-4" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link href="/settings">
                <Settings className="h-4 w-4" />
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <button className="w-full">
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      
      <SidebarRail />
    </Sidebar>
  )
}
