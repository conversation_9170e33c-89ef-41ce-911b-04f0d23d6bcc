'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, GraduationCap, Users2, DollarSign, TrendingUp, TrendingDown } from 'lucide-react'

// Mock data for the dashboard
const statsData = [
  {
    title: 'Students',
    value: '15.00K',
    icon: Users,
    color: 'bg-purple-100 text-purple-600',
    bgColor: 'bg-purple-50',
  },
  {
    title: 'Teachers',
    value: '2.00K',
    icon: GraduationCap,
    color: 'bg-blue-100 text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    title: 'Parents',
    value: '5.6K',
    icon: Users2,
    color: 'bg-orange-100 text-orange-600',
    bgColor: 'bg-orange-50',
  },
  {
    title: 'Earnings',
    value: '$19.3K',
    icon: DollarSign,
    color: 'bg-green-100 text-green-600',
    bgColor: 'bg-green-50',
  },
]

const starStudents = [
  {
    id: 1,
    name: '<PERSON>',
    studentId: 'PRE43178',
    marks: 1185,
    percent: 98,
    year: 2014,
    avatar: '/api/placeholder/40/40',
  },
  {
    id: 2,
    name: '<PERSON> Plenty',
    studentId: 'PRE43174',
    marks: 1165,
    percent: 91,
    year: 2014,
    avatar: '/api/placeholder/40/40',
  },
  {
    id: 3,
    name: 'John Miller',
    studentId: 'PRE43187',
    marks: 1175,
    percent: 92,
    year: 2014,
    avatar: '/api/placeholder/40/40',
  },
]

const examResults = [
  {
    title: 'New Teacher',
    description: 'It is a long established readable.',
    time: 'Just now',
    icon: '👨‍🏫',
    color: 'bg-blue-100',
  },
  {
    title: 'Fees Structure',
    description: 'It is a long established readable.',
    time: 'Today',
    icon: '💰',
    color: 'bg-red-100',
  },
  {
    title: 'New Course',
    description: 'It is a long established readable.',
    time: '26 Sep 2023',
    icon: '📚',
    color: 'bg-green-100',
  },
]

export function DashboardOverview() {
  return (
    <div className="space-y-6 p-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statsData.map((stat, index) => (
          <Card key={index} className={`${stat.bgColor} border-0`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Exam Results Chart */}
        <Card>
          <CardHeader>
            <CardTitle>All Exam Result</CardTitle>
            <CardDescription>Students & Teacher</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl font-bold text-muted-foreground mb-2">📊</div>
                <p className="text-muted-foreground">Chart visualization would go here</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Monthly exam results comparison
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Students Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-[200px]">
              <div className="relative">
                <div className="w-32 h-32 rounded-full bg-gradient-to-r from-purple-400 to-orange-400 flex items-center justify-center">
                  <div className="w-20 h-20 rounded-full bg-white flex items-center justify-center">
                    <span className="text-2xl font-bold">15000</span>
                  </div>
                </div>
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <span className="text-sm font-medium">Total</span>
                </div>
              </div>
            </div>
            <div className="flex justify-center gap-6 mt-8">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-purple-400"></div>
                <span className="text-sm">Male</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-400"></div>
                <span className="text-sm">Female</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Star Students */}
        <Card>
          <CardHeader>
            <CardTitle>Star Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {starStudents.map((student) => (
                <div key={student.id} className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center text-white font-medium">
                    {student.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{student.name}</div>
                    <div className="text-sm text-muted-foreground">{student.studentId}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{student.marks}</div>
                    <div className="text-sm text-muted-foreground">{student.percent}%</div>
                  </div>
                  <div className="text-sm text-muted-foreground">{student.year}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* All Exam Results */}
        <Card>
          <CardHeader>
            <CardTitle>All Exam Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {examResults.map((result, index) => (
                <div key={index} className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50">
                  <div className={`w-10 h-10 rounded-full ${result.color} flex items-center justify-center text-lg`}>
                    {result.icon}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{result.title}</div>
                    <div className="text-sm text-muted-foreground">{result.description}</div>
                  </div>
                  <div className="text-sm text-muted-foreground">{result.time}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
