import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database tables
export interface Student {
  id: string
  user_id?: string
  name: string
  email: string
  grade: string
  role: string
  created_at: string
}

export interface StudentFormData {
  name: string
  email: string
  grade: string
}
