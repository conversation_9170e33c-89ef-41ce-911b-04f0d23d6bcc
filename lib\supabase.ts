import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database tables
export interface Student {
  id: string
  user_id?: string
  name: string
  email: string
  grade: string
  parent_name: string
  parent_mobile: string
  address: string
  date_of_birth: string
  role: string
  created_at: string
}

export interface StudentFormData {
  name: string
  email: string
  grade: string
  parentName: string
  parentMobile: string
  address: string
  dateOfBirth: string
}

// Database functions
export async function getStudents(): Promise<Student[]> {
  const { data, error } = await supabase
    .from('students')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching students:', error)
    throw error
  }

  return data || []
}

export async function addStudent(studentData: StudentFormData): Promise<Student> {
  const { data, error } = await supabase
    .from('students')
    .insert([{
      name: studentData.name,
      email: studentData.email,
      grade: studentData.grade,
      parent_name: studentData.parentName,
      parent_mobile: studentData.parentMobile,
      address: studentData.address,
      date_of_birth: studentData.dateOfBirth,
      role: 'student'
    }])
    .select()
    .single()

  if (error) {
    console.error('Error adding student:', error)
    throw error
  }

  return data
}

export async function updateStudent(id: string, studentData: Partial<StudentFormData>): Promise<Student> {
  const updateData: any = {}

  if (studentData.name) updateData.name = studentData.name
  if (studentData.email) updateData.email = studentData.email
  if (studentData.grade) updateData.grade = studentData.grade
  if (studentData.parentName) updateData.parent_name = studentData.parentName
  if (studentData.parentMobile) updateData.parent_mobile = studentData.parentMobile
  if (studentData.address) updateData.address = studentData.address
  if (studentData.dateOfBirth) updateData.date_of_birth = studentData.dateOfBirth

  const { data, error } = await supabase
    .from('students')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating student:', error)
    throw error
  }

  return data
}

export async function deleteStudent(id: string): Promise<void> {
  const { error } = await supabase
    .from('students')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting student:', error)
    throw error
  }
}
